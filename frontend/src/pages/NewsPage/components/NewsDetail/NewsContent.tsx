import React, { useState, useEffect } from 'react';
import { parseContent, isEntityDataAvailable } from '../../../../utils/contentParser';
import { entityDataManager } from '../../../../services/entityDataManager';

interface NewsContentProps {
  content?: string;
  showOriginal?: boolean;
  content_cn?: string;
}

export function NewsContent({ content = '', showOriginal = false, content_cn }: NewsContentProps) {
  const displayContent = showOriginal ? content : (content_cn || content);
  const [entityDataReady, setEntityDataReady] = useState(isEntityDataAvailable());

  // 监听实体数据状态变化
  useEffect(() => {
    const checkEntityData = () => {
      setEntityDataReady(isEntityDataAvailable());
    };

    // 设置监听器
    entityDataManager.setListeners({
      onUpdateComplete: checkEntityData,
      onStatusChange: checkEntityData
    });

    // 初始检查
    checkEntityData();

    // 定期检查实体数据是否可用
    const interval = setInterval(checkEntityData, 5000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  const processedContent = React.useMemo(() => {
    if (!displayContent) {
      return '暂无内容';
    }

    // 只有在实体数据可用时才进行实体解析
    if (entityDataReady) {
      return parseContent(displayContent);
    } else {
      // 如果实体数据不可用，返回原始内容
      return displayContent;
    }
  }, [displayContent, entityDataReady]);

  return (
    <div
      className="prose prose-invert max-w-none"
      dangerouslySetInnerHTML={{ __html: processedContent }}
    />
  );
}