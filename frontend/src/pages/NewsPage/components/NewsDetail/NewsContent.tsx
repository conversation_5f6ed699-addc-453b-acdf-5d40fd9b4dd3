import React, { useState, useEffect } from 'react';
import { parseContent, parseBilingualContent, isEntityDataAvailable } from '../../../../utils/contentParser';
import { entityDataManager } from '../../../../services/entityDataManager';

interface NewsContentProps {
  content?: string;
  showOriginal?: boolean;
  content_cn?: string;
}

export function NewsContent({ content = '', showOriginal = false, content_cn }: NewsContentProps) {
  const [entityDataReady, setEntityDataReady] = useState(isEntityDataAvailable());

  // 监听实体数据状态变化
  useEffect(() => {
    const checkEntityData = () => {
      setEntityDataReady(isEntityDataAvailable());
    };

    // 设置监听器
    entityDataManager.setListeners({
      onUpdateComplete: checkEntityData,
      onStatusChange: checkEntityData
    });

    // 初始检查
    checkEntityData();

    // 定期检查实体数据是否可用
    const interval = setInterval(checkEntityData, 5000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  const processedContent = React.useMemo(() => {
    // 如果没有内容，返回默认文本
    if (!content && !content_cn) {
      return '暂无内容';
    }

    // 如果实体数据不可用，返回原始内容
    if (!entityDataReady) {
      return showOriginal ? (content || '') : (content_cn || content || '');
    }

    // 如果只有一种语言的内容，直接解析
    if (!content_cn || !content) {
      const singleContent = content_cn || content || '';
      return parseContent(singleContent);
    }

    // 如果有双语内容，使用双语解析确保一致性
    const bilingualResult = parseBilingualContent(content, content_cn);
    return showOriginal ? bilingualResult.original : bilingualResult.translated;

  }, [content, content_cn, showOriginal, entityDataReady]);

  return (
    <div
      className="prose prose-invert max-w-none"
      dangerouslySetInnerHTML={{ __html: processedContent }}
    />
  );
}