import React, { useState, useEffect } from 'react';
import {
  parseContent,
  isEntityDataAvailable,
  getEntityStats,
  debugEntityMatching,
  getSupportedTranslations
} from '../../utils/contentParser';
import { entityDataManager } from '../../services/entityDataManager';

export function EntityParserTest() {
  const [testContent, setTestContent] = useState(`
    SpaceX is preparing for its 490th Falcon 9 rocket, which will carry a batch of 23 of its Starlink V2 Mini satellites into low Earth orbit on Friday. The mission, dubbed Starlink 12-26, will launch from Space Launch Complex 40 at Cape Canaveral Space Force Station. SpaceX is aiming for liftoff at 11:29 a.m. EDT (1529 UTC). Spaceflight Now will have live coverage beginning about an hour prior to liftoff. The 45th Weather Squadron forecast an 85 percent chance of favorable weather at the opening of the original, four-hour launch window, which opened at 7:45 a.m. EDT (1145 UTC). However, conditions drop to 60 percent favorable towards the end of the window.

    星链12-26任务定于今为止第490次猎鹰9号火箭发射。SpaceX 目标是在东部夏令时间上午11点29分（UTC 1529）从卡纳维拉尔角太空军基地40号发射台升空。
  `);
  
  const [parsedContent, setParsedContent] = useState('');
  const [entityDataReady, setEntityDataReady] = useState(false);
  const [entityStats, setEntityStats] = useState({
    satellites: 0,
    constellations: 0,
    rockets: 0,
    providers: 0,
    launchSites: 0,
    total: 0
  });
  const [debugInfo, setDebugInfo] = useState<any>(null);

  useEffect(() => {
    const updateStatus = () => {
      const ready = isEntityDataAvailable();
      const stats = getEntityStats();
      setEntityDataReady(ready);
      setEntityStats(stats);
      
      if (ready) {
        setParsedContent(parseContent(testContent));
        setDebugInfo(debugEntityMatching(testContent));
      }
    };

    // 设置监听器
    entityDataManager.setListeners({
      onUpdateComplete: updateStatus,
      onStatusChange: updateStatus
    });

    // 初始检查
    updateStatus();

    // 定期检查
    const interval = setInterval(updateStatus, 2000);

    return () => {
      clearInterval(interval);
    };
  }, [testContent]);

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    setTestContent(newContent);
    
    if (entityDataReady) {
      setParsedContent(parseContent(newContent));
      setDebugInfo(debugEntityMatching(newContent));
    }
  };

  const handleRefreshData = async () => {
    try {
      await entityDataManager.refreshData();
    } catch (error) {
      console.error('刷新实体数据失败:', error);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4 text-white">实体解析测试</h1>
      
      {/* 状态信息 */}
      <div className="mb-6 p-4 bg-gray-800 rounded-lg">
        <h2 className="text-lg font-semibold mb-2 text-white">实体数据状态</h2>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">数据可用: </span>
            <span className={entityDataReady ? 'text-green-400' : 'text-red-400'}>
              {entityDataReady ? '是' : '否'}
            </span>
          </div>
          <div>
            <span className="text-gray-400">总实体数: </span>
            <span className="text-white">{entityStats.total}</span>
          </div>
          <div>
            <span className="text-gray-400">卫星: </span>
            <span className="text-white">{entityStats.satellites}</span>
          </div>
          <div>
            <span className="text-gray-400">星座: </span>
            <span className="text-white">{entityStats.constellations}</span>
          </div>
          <div>
            <span className="text-gray-400">火箭: </span>
            <span className="text-white">{entityStats.rockets}</span>
          </div>
          <div>
            <span className="text-gray-400">服务商: </span>
            <span className="text-white">{entityStats.providers}</span>
          </div>
          <div>
            <span className="text-gray-400">发射场: </span>
            <span className="text-white">{entityStats.launchSites}</span>
          </div>
        </div>
        <button
          onClick={handleRefreshData}
          className="mt-3 px-4 py-2 bg-blue-500 hover:bg-blue-600 rounded text-white text-sm"
        >
          刷新实体数据
        </button>
      </div>

      {/* 测试内容输入 */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-2 text-white">测试内容</h2>
        <textarea
          value={testContent}
          onChange={handleContentChange}
          className="w-full h-32 p-3 bg-gray-800 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
          placeholder="输入包含实体名称的文本..."
        />
      </div>

      {/* 解析结果 */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-2 text-white">解析结果</h2>
        <div
          className="p-4 bg-gray-800 rounded-lg text-white min-h-32"
          dangerouslySetInnerHTML={{ __html: parsedContent || '等待解析...' }}
        />
      </div>

      {/* 调试信息 */}
      {debugInfo && (
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2 text-white">调试信息</h2>
          <div className="p-4 bg-gray-800 rounded-lg text-white">
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <span className="text-gray-400">总匹配数: </span>
                <span className="text-white">{debugInfo.analysis.totalMatches}</span>
              </div>
              <div>
                <span className="text-gray-400">重叠数: </span>
                <span className="text-white">{debugInfo.analysis.overlaps}</span>
              </div>
              <div>
                <span className="text-gray-400">无效边界: </span>
                <span className="text-white">{debugInfo.analysis.invalidBoundaries}</span>
              </div>
              <div>
                <span className="text-gray-400">最终实体数: </span>
                <span className="text-white">{debugInfo.entities.length}</span>
              </div>
            </div>

            <div className="mb-4">
              <h3 className="text-md font-semibold mb-2 text-white">按类型统计:</h3>
              <div className="grid grid-cols-3 gap-2 text-sm">
                {Object.entries(debugInfo.analysis.byType).map(([type, count]) => (
                  <div key={type}>
                    <span className="text-gray-400">{type}: </span>
                    <span className="text-white">{count}</span>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-md font-semibold mb-2 text-white">检测到的实体:</h3>
              <div className="space-y-1 text-sm">
                {debugInfo.entities.map((entity: any, index: number) => (
                  <div key={index} className="flex justify-between">
                    <span className="text-blue-400">{entity.displayName}</span>
                    <span className="text-gray-400">
                      {entity.type} ({entity.startIndex}-{entity.endIndex})
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 支持的翻译对照表 */}
      <div>
        <h2 className="text-lg font-semibold mb-2 text-white">支持的翻译对照</h2>
        <div className="p-4 bg-gray-800 rounded-lg text-white">
          <div className="space-y-2 text-sm">
            {Object.entries(getSupportedTranslations()).map(([original, translations]) => (
              <div key={original} className="flex justify-between">
                <span className="text-blue-400">{original}</span>
                <span className="text-gray-400">{translations.join(', ')}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
