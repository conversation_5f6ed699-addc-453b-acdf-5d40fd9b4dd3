import React, { useState, useEffect } from 'react';
import {
  parseContent,
  isEntityDataAvailable,
  getEntityStats,
  debugEntityMatching,
  getSupportedTranslations
} from '../../utils/contentParser';
import { entityDataManager } from '../../services/entityDataManager';

export function EntityParserTest() {
  const [testContent, setTestContent] = useState(`
    SpaceX公司的Falcon 9火箭从Kennedy Space Center发射了Starlink卫星。
    这次发射任务由SpaceX作为发射服务商执行，目标是将卫星送入轨道。
    肯尼迪航天中心是美国重要的发射场之一。

    测试部分匹配问题：
    - "Falcon"应该不会被高亮（因为只是"Falcon 9"的一部分）
    - "Space"应该不会被高亮（因为只是"SpaceX"的一部分）
    - "猎鹰9号"应该被正确识别并链接到Falcon 9

    测试完整匹配：
    - "Falcon 9" 完整匹配应该被高亮
    - "SpaceX" 完整匹配应该被高亮
    - "Kennedy Space Center" 完整匹配应该被高亮
  `);
  
  const [parsedContent, setParsedContent] = useState('');
  const [entityDataReady, setEntityDataReady] = useState(false);
  const [entityStats, setEntityStats] = useState({
    satellites: 0,
    constellations: 0,
    rockets: 0,
    providers: 0,
    launchSites: 0,
    total: 0
  });
  const [debugInfo, setDebugInfo] = useState<any>(null);

  useEffect(() => {
    const updateStatus = () => {
      const ready = isEntityDataAvailable();
      const stats = getEntityStats();
      setEntityDataReady(ready);
      setEntityStats(stats);
      
      if (ready) {
        setParsedContent(parseContent(testContent));
        setDebugInfo(debugEntityMatching(testContent));
      }
    };

    // 设置监听器
    entityDataManager.setListeners({
      onUpdateComplete: updateStatus,
      onStatusChange: updateStatus
    });

    // 初始检查
    updateStatus();

    // 定期检查
    const interval = setInterval(updateStatus, 2000);

    return () => {
      clearInterval(interval);
    };
  }, [testContent]);

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    setTestContent(newContent);
    
    if (entityDataReady) {
      setParsedContent(parseContent(newContent));
      setDebugInfo(debugEntityMatching(newContent));
    }
  };

  const handleRefreshData = async () => {
    try {
      await entityDataManager.refreshData();
    } catch (error) {
      console.error('刷新实体数据失败:', error);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4 text-white">实体解析测试</h1>
      
      {/* 状态信息 */}
      <div className="mb-6 p-4 bg-gray-800 rounded-lg">
        <h2 className="text-lg font-semibold mb-2 text-white">实体数据状态</h2>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">数据可用: </span>
            <span className={entityDataReady ? 'text-green-400' : 'text-red-400'}>
              {entityDataReady ? '是' : '否'}
            </span>
          </div>
          <div>
            <span className="text-gray-400">总实体数: </span>
            <span className="text-white">{entityStats.total}</span>
          </div>
          <div>
            <span className="text-gray-400">卫星: </span>
            <span className="text-white">{entityStats.satellites}</span>
          </div>
          <div>
            <span className="text-gray-400">星座: </span>
            <span className="text-white">{entityStats.constellations}</span>
          </div>
          <div>
            <span className="text-gray-400">火箭: </span>
            <span className="text-white">{entityStats.rockets}</span>
          </div>
          <div>
            <span className="text-gray-400">服务商: </span>
            <span className="text-white">{entityStats.providers}</span>
          </div>
          <div>
            <span className="text-gray-400">发射场: </span>
            <span className="text-white">{entityStats.launchSites}</span>
          </div>
        </div>
        <button
          onClick={handleRefreshData}
          className="mt-3 px-4 py-2 bg-blue-500 hover:bg-blue-600 rounded text-white text-sm"
        >
          刷新实体数据
        </button>
      </div>

      {/* 测试内容输入 */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-2 text-white">测试内容</h2>
        <textarea
          value={testContent}
          onChange={handleContentChange}
          className="w-full h-32 p-3 bg-gray-800 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
          placeholder="输入包含实体名称的文本..."
        />
      </div>

      {/* 解析结果 */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-2 text-white">解析结果</h2>
        <div
          className="p-4 bg-gray-800 rounded-lg text-white min-h-32"
          dangerouslySetInnerHTML={{ __html: parsedContent || '等待解析...' }}
        />
      </div>

      {/* 调试信息 */}
      {debugInfo && (
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2 text-white">调试信息</h2>
          <div className="p-4 bg-gray-800 rounded-lg text-white">
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <span className="text-gray-400">总匹配数: </span>
                <span className="text-white">{debugInfo.analysis.totalMatches}</span>
              </div>
              <div>
                <span className="text-gray-400">重叠数: </span>
                <span className="text-white">{debugInfo.analysis.overlaps}</span>
              </div>
              <div>
                <span className="text-gray-400">无效边界: </span>
                <span className="text-white">{debugInfo.analysis.invalidBoundaries}</span>
              </div>
              <div>
                <span className="text-gray-400">最终实体数: </span>
                <span className="text-white">{debugInfo.entities.length}</span>
              </div>
            </div>

            <div className="mb-4">
              <h3 className="text-md font-semibold mb-2 text-white">按类型统计:</h3>
              <div className="grid grid-cols-3 gap-2 text-sm">
                {Object.entries(debugInfo.analysis.byType).map(([type, count]) => (
                  <div key={type}>
                    <span className="text-gray-400">{type}: </span>
                    <span className="text-white">{count}</span>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-md font-semibold mb-2 text-white">检测到的实体:</h3>
              <div className="space-y-1 text-sm">
                {debugInfo.entities.map((entity: any, index: number) => (
                  <div key={index} className="flex justify-between">
                    <span className="text-blue-400">{entity.displayName}</span>
                    <span className="text-gray-400">
                      {entity.type} ({entity.startIndex}-{entity.endIndex})
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 支持的翻译对照表 */}
      <div>
        <h2 className="text-lg font-semibold mb-2 text-white">支持的翻译对照</h2>
        <div className="p-4 bg-gray-800 rounded-lg text-white">
          <div className="space-y-2 text-sm">
            {Object.entries(getSupportedTranslations()).map(([original, translations]) => (
              <div key={original} className="flex justify-between">
                <span className="text-blue-400">{original}</span>
                <span className="text-gray-400">{translations.join(', ')}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
