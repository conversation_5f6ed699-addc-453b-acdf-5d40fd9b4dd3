import React, { useState, useEffect } from 'react';
import {
  parseContent,
  parseBilingualContent,
  isEntityDataAvailable,
  getEntityStats,
  debugEntityMatching,
  getSupportedTranslations
} from '../../utils/contentParser';
import { entityDataManager } from '../../services/entityDataManager';

export function EntityParserTest() {
  // 英文原文 (从截图中提取)
  const [englishContent] = useState(`
    SpaceX is preparing for its 490th Falcon 9 rocket, which will carry a batch of 23 of its Starlink V2 Mini satellites into low Earth orbit on Friday. The mission, dubbed Starlink 12-26, will launch from Space Launch Complex 40 at Cape Canaveral Space Force Station. SpaceX is aiming for liftoff at 11:29 a.m. EDT (1529 UTC). Spaceflight Now will have live coverage beginning about an hour prior to liftoff. The 45th Weather Squadron forecast an 85 percent chance of favorable weather at the opening of the original, four-hour launch window, which opened at 7:45 a.m. EDT (1145 UTC). However, conditions drop to 60 percent favorable towards the end of the window. "The Atlantic ridge will remain to the north of Central Florida through late week, with a continuation of low-level onshore flow," launch weather officers wrote on Thursday. "Some decrease in column moisture through the atmosphere is noted into the weekend as another round of Saharan dust influences the local atmosphere. "However, this regime will still support onshore-moving showers during the night and morning hours along the coast, with early daily seabreeze formation focusing more widespread shower and thunderstorm activity inland during the afternoon and evening hours." SpaceX will use the Falcon 9 first stage booster with the tail number B1078, which will launch for a 21st time. Its previous flights include NASA's Crew-6, ASTSpaceMobile's Bluebird 1-5 and USSF-124. A little more than eight minutes after liftoff, B1078 will target a landing on the droneship, 'A Shortfall of Gravitas.' If successful, this will be the 113th touchdown on this vessel and the 462nd booster landing to date. Onboard the Falcon 9 rocket are 23 Starlink V2 Mini satellites, of which 13 have direct-to-cell capabilities. To date, SpaceX has launched 661 of these DTC Starlink satellites, with 273 of them sent to low Earth orbit in 2025 so far.
  `);

  // 中文翻译 (从截图中提取)
  const [chineseContent] = useState(`
    星链12-26任务定于今为止第490次猎鹰9号火箭发射。SpaceX 目标是在东部夏令时间上午11点29分（UTC 1529）从卡纳维拉尔角太空军基地40号发射台升空。SpaceX 正在准备其第490次猎鹰9号火箭发射，该火箭将于周五将一批23颗星链V2 Mini卫星送入近地轨道。此次名为星链12-26的任务将从卡纳维拉尔角太空军基地40号发射台升空。SpaceX 目标是在东部夏令时间上午11点29分（UTC 1529）升空。Spaceflight Now 将在升空前约一小时开始进行现场直播。45气象中队预报原始四小时发射窗口（东部夏令时间上午7点45分开启，UTC 1145）的天气条件有利概率为85%。然而，到窗口结束时，条件下降至60%。"大西洋高压脊将保持在佛罗里达中部北，低层沿海流将持续存在，"发射气象官员周四写道。"随着另一轮撒哈拉尘埃响当地大气，周末大气中的水分有所减少。""然而，这种天气模式仍将支持沿海向内陆移动的降雨，白天早期海风形成将集中在下午和晚上更广泛的降雨和雷暴活动内陆地区。" SpaceX 将使用编号B1078的第一级助推器，这是它第21次飞行。此前的飞行包括NASA的Crew-6任务、ASTSpaceMobile的Bluebird 1-5以及USSF-124任务。发射约八分钟后，B1078将目标锁定在无人机船上"Gravitas的短缺"上。如果成功，这将是该船上的第113次着陆，也是迄今为止第462次助推器着陆。猎鹰9号火箭上搭载了23颗星链V2 Mini卫星，其中13颗具备直接到蜂窝功能。截至目前，SpaceX 已经发射了661颗这些DTC星链卫星，其中273颗已于2025年被送入近地轨道。
  `);

  const [testContent, setTestContent] = useState(englishContent);
  
  const [parsedContent, setParsedContent] = useState('');
  const [bilingualResult, setBilingualResult] = useState<{original: string; translated: string} | null>(null);
  const [entityDataReady, setEntityDataReady] = useState(false);
  const [entityStats, setEntityStats] = useState({
    satellites: 0,
    constellations: 0,
    rockets: 0,
    providers: 0,
    launchSites: 0,
    total: 0
  });
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [currentLanguage, setCurrentLanguage] = useState<'english' | 'chinese' | 'bilingual'>('english');

  useEffect(() => {
    const updateStatus = () => {
      const ready = isEntityDataAvailable();
      const stats = getEntityStats();
      setEntityDataReady(ready);
      setEntityStats(stats);
      
      if (ready) {
        // 单语解析
        setParsedContent(parseContent(testContent));
        setDebugInfo(debugEntityMatching(testContent));

        // 双语解析
        const bilingual = parseBilingualContent(englishContent, chineseContent);
        setBilingualResult(bilingual);
      }
    };

    // 设置监听器
    entityDataManager.setListeners({
      onUpdateComplete: updateStatus,
      onStatusChange: updateStatus
    });

    // 初始检查
    updateStatus();

    // 定期检查
    const interval = setInterval(updateStatus, 2000);

    return () => {
      clearInterval(interval);
    };
  }, [testContent]);

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    setTestContent(newContent);

    if (entityDataReady) {
      setParsedContent(parseContent(newContent));
      setDebugInfo(debugEntityMatching(newContent));
    }
  };

  const handleLanguageSwitch = (language: 'english' | 'chinese' | 'bilingual') => {
    setCurrentLanguage(language);
    if (language === 'english') {
      setTestContent(englishContent);
    } else if (language === 'chinese') {
      setTestContent(chineseContent);
    }
  };

  const handleRefreshData = async () => {
    try {
      await entityDataManager.refreshData();
    } catch (error) {
      console.error('刷新实体数据失败:', error);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4 text-white">实体解析测试</h1>
      
      {/* 状态信息 */}
      <div className="mb-6 p-4 bg-gray-800 rounded-lg">
        <h2 className="text-lg font-semibold mb-2 text-white">实体数据状态</h2>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">数据可用: </span>
            <span className={entityDataReady ? 'text-green-400' : 'text-red-400'}>
              {entityDataReady ? '是' : '否'}
            </span>
          </div>
          <div>
            <span className="text-gray-400">总实体数: </span>
            <span className="text-white">{entityStats.total}</span>
          </div>
          <div>
            <span className="text-gray-400">卫星: </span>
            <span className="text-white">{entityStats.satellites}</span>
          </div>
          <div>
            <span className="text-gray-400">星座: </span>
            <span className="text-white">{entityStats.constellations}</span>
          </div>
          <div>
            <span className="text-gray-400">火箭: </span>
            <span className="text-white">{entityStats.rockets}</span>
          </div>
          <div>
            <span className="text-gray-400">服务商: </span>
            <span className="text-white">{entityStats.providers}</span>
          </div>
          <div>
            <span className="text-gray-400">发射场: </span>
            <span className="text-white">{entityStats.launchSites}</span>
          </div>
        </div>
        <button
          onClick={handleRefreshData}
          className="mt-3 px-4 py-2 bg-blue-500 hover:bg-blue-600 rounded text-white text-sm"
        >
          刷新实体数据
        </button>
      </div>

      {/* 语言切换 */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-2 text-white">测试模式</h2>
        <div className="flex gap-2">
          <button
            onClick={() => handleLanguageSwitch('english')}
            className={`px-4 py-2 rounded transition-colors ${
              currentLanguage === 'english'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            英文测试
          </button>
          <button
            onClick={() => handleLanguageSwitch('chinese')}
            className={`px-4 py-2 rounded transition-colors ${
              currentLanguage === 'chinese'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            中文测试
          </button>
          <button
            onClick={() => handleLanguageSwitch('bilingual')}
            className={`px-4 py-2 rounded transition-colors ${
              currentLanguage === 'bilingual'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            双语对比
          </button>
        </div>
      </div>

      {/* 测试内容输入 */}
      {currentLanguage !== 'bilingual' && (
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2 text-white">测试内容</h2>
        <textarea
          value={testContent}
          onChange={handleContentChange}
          className="w-full h-32 p-3 bg-gray-800 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
          placeholder="输入包含实体名称的文本..."
        />
        </div>
      )}

      {/* 双语对比显示 */}
      {currentLanguage === 'bilingual' && bilingualResult && (
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2 text-white">双语对比解析结果</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div>
              <h3 className="text-md font-semibold mb-2 text-blue-400">英文原版</h3>
              <div
                className="p-4 bg-gray-800 rounded-lg text-white min-h-32"
                dangerouslySetInnerHTML={{ __html: bilingualResult.original || '等待解析...' }}
              />
            </div>
            <div>
              <h3 className="text-md font-semibold mb-2 text-green-400">中文翻译</h3>
              <div
                className="p-4 bg-gray-800 rounded-lg text-white min-h-32"
                dangerouslySetInnerHTML={{ __html: bilingualResult.translated || '等待解析...' }}
              />
            </div>
          </div>
        </div>
      )}

      {/* 单语解析结果 */}
      {currentLanguage !== 'bilingual' && (
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2 text-white">解析结果</h2>
          <div
            className="p-4 bg-gray-800 rounded-lg text-white min-h-32"
            dangerouslySetInnerHTML={{ __html: parsedContent || '等待解析...' }}
          />
        </div>
      )}

      {/* 调试信息 */}
      {debugInfo && (
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2 text-white">调试信息</h2>
          <div className="p-4 bg-gray-800 rounded-lg text-white">
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <span className="text-gray-400">总匹配数: </span>
                <span className="text-white">{debugInfo.analysis.totalMatches}</span>
              </div>
              <div>
                <span className="text-gray-400">重叠数: </span>
                <span className="text-white">{debugInfo.analysis.overlaps}</span>
              </div>
              <div>
                <span className="text-gray-400">无效边界: </span>
                <span className="text-white">{debugInfo.analysis.invalidBoundaries}</span>
              </div>
              <div>
                <span className="text-gray-400">最终实体数: </span>
                <span className="text-white">{debugInfo.entities.length}</span>
              </div>
            </div>

            <div className="mb-4">
              <h3 className="text-md font-semibold mb-2 text-white">按类型统计:</h3>
              <div className="grid grid-cols-3 gap-2 text-sm">
                {Object.entries(debugInfo.analysis.byType).map(([type, count]) => (
                  <div key={type}>
                    <span className="text-gray-400">{type}: </span>
                    <span className="text-white">{count}</span>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-md font-semibold mb-2 text-white">检测到的实体:</h3>
              <div className="space-y-1 text-sm">
                {debugInfo.entities.map((entity: any, index: number) => (
                  <div key={index} className="flex justify-between">
                    <span className="text-blue-400">{entity.displayName}</span>
                    <span className="text-gray-400">
                      {entity.type} ({entity.startIndex}-{entity.endIndex})
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 支持的翻译对照表 */}
      <div>
        <h2 className="text-lg font-semibold mb-2 text-white">支持的翻译对照</h2>
        <div className="p-4 bg-gray-800 rounded-lg text-white">
          <div className="space-y-2 text-sm">
            {Object.entries(getSupportedTranslations()).map(([original, translations]) => (
              <div key={original} className="flex justify-between">
                <span className="text-blue-400">{original}</span>
                <span className="text-gray-400">{translations.join(', ')}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
