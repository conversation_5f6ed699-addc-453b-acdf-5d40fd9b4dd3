import 'cesium/Build/Cesium/Widgets/widgets.css';
import './index.css';
import React from 'react';
import ReactDOM from 'react-dom/client';
import { RouterProvider } from 'react-router-dom';
import { router } from './router';
import { tleDataManager } from './services/tleDataManager';
import { constellationDataManager } from './services/constellationDataManager';
import { entityDataManager } from './services/entityDataManager';

// 初始化TLE数据管理器
tleDataManager.init().catch(error => {
  console.error('TLE数据管理器初始化失败:', error);
});

// 初始化星座数据管理器
constellationDataManager.init().catch(error => {
  console.error('星座数据管理器初始化失败:', error);
});

// 初始化实体数据管理器
entityDataManager.init().catch(error => {
  console.error('实体数据管理器初始化失败:', error);
});

// 只在开发模式下导入Stagewise组件
let StagewiseWrapper: React.ComponentType | null = null;
if (process.env.NODE_ENV === 'development') {
  StagewiseWrapper = React.lazy(() => import('./components/DevTools/StagewiseWrapper'));
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <RouterProvider router={router} />
    {/* 只在开发模式下渲染Stagewise工具栏 */}
    {process.env.NODE_ENV === 'development' && StagewiseWrapper && (
      <React.Suspense fallback={null}>
        <StagewiseWrapper />
      </React.Suspense>
    )}
  </React.StrictMode>,
);