/**
 * 实体数据类型定义
 */

// 卫星名称数据
export interface SatelliteNameData {
  names: string[];
  lastUpdated: string;
}

// 星座名称数据
export interface ConstellationNameData {
  constellationNames: string[];
  lastUpdated: string;
}

// 火箭型号数据
export interface RocketNameData {
  success: boolean;
  data: string[];
  lastUpdated: string;
}

// 发射服务商数据
export interface LaunchProviderData {
  success: boolean;
  data: string[];
  lastUpdated: string;
}

// 发射场名称数据（从launchSite.ts文件获取）
export interface LaunchSiteNameData {
  names: string[];
  lastUpdated: string;
}

// 实体缓存数据
export interface EntityCacheData {
  id: string;
  lastUpdated: string;
  nextUpdate: string;
  satellites: SatelliteNameData;
  constellations: ConstellationNameData;
  rockets: RocketNameData;
  providers: LaunchProviderData;
  launchSites: LaunchSiteNameData;
}

// 实体类型枚举
export enum EntityType {
  SATELLITE = 'satellite',
  CONSTELLATION = 'constellation',
  ROCKET = 'rocket',
  PROVIDER = 'provider',
  LAUNCH_SITE = 'launchSite'
}

// 实体匹配结果
export interface EntityMatch {
  type: EntityType;
  name: string;
  displayName: string;
  url: string;
  startIndex: number;
  endIndex: number;
}

// 实体数据状态
export interface EntityDataStatus {
  hasData: boolean;
  isExpired: boolean;
  lastUpdated: string | null;
  nextUpdate: string | null;
  isUpdating: boolean;
  lastError: string | null;
  entityCounts: {
    satellites: number;
    constellations: number;
    rockets: number;
    providers: number;
    launchSites: number;
  };
}
