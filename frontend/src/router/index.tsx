import React from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import { HomePage } from '../pages/HomePage';
import { LaunchPage } from '../pages/LaunchPage';
import { NewsPage } from '../pages/NewsPage';
import { NewsDetail } from '../pages/NewsPage/components/NewsDetail/index';
import { AnalysisPage } from '../pages/AnalysisPage';
import { AnalysisDetail } from '../pages/AnalysisPage/components/AnalysisDetail';
import { SatelliteDetailPage } from '../pages/SatelliteDetail';
import { ConstellationDetailPage } from '../pages/ConstellationDetail';
import { DebrisDetailPage } from '../pages/DebrisDetail';
import { SpacecraftDetailPage } from '../pages/SpacecraftDetail';
import { LaunchSitePage } from '../pages/LaunchSitePage';
import { LaunchSiteDetailPage } from '../pages/LaunchSiteDetail';
import { LaunchProviderDetailPage } from '../pages/LaunchProviderDetail';
import { RocketPage } from '../pages/RocketPage';
import { ErrorBoundary } from '../components/common/ErrorBoundary';
import { Tianzhou6Page } from '../pages/PayloadDetail/pages/Tianzhou6Page';
import Search from '../components/search/Search';
import { SpaceSituationPage } from '../pages/SpaceSituationPage';
import { ProfilePage } from '../pages/Settings/ProfilePage';
import { FollowsPage } from '../pages/Settings/FollowsPage';
import { KeywordsPage } from '../pages/Settings/KeywordsPage';
import { MembershipPage } from '../pages/Settings/MembershipPage';
import { EntityOverviewPage } from '../pages/EntityOverviewPage';
import { KeywordNewsPage } from '../pages/KeywordNewsPage';
import { RemindersPage } from '../pages/RemindersPage';
import { AlertsPage } from '../pages/AlertsPage';
import LoginPage from '../pages/LoginPage';
import { AdminPage } from '../pages/AdminPage';
import { AdminRoute } from '../components/common/ProtectedRoute';
import { EntityParserTest } from '../components/test/EntityParserTest';

export const router = createBrowserRouter([
  {
    path: '/',
    element: <HomePage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/login',
    element: <LoginPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/admin',
    element: (
      <AdminRoute>
        <AdminPage />
      </AdminRoute>
    ),
    errorElement: <ErrorBoundary />
  },
  {
    path: '/search',
    element: <Search />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/launches',
    element: <LaunchPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/news',
    element: <NewsPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/news/:id',
    element: <NewsDetail />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/analysis',
    element: <AnalysisPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/analysis/:category',
    element: <AnalysisDetail />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/spacecraft/tz-6',
    element: <Tianzhou6Page />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/spacecraft/:id',
    element: <SpacecraftDetailPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/satellite/:id',
    element: <SatelliteDetailPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/constellation/:id',
    element: <ConstellationDetailPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/debris/:id',
    element: <DebrisDetailPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/sites/:id',
    element: <LaunchSitePage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/launch-site/:siteName',
    element: <LaunchSiteDetailPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/launch-provider/:providerName',
    element: <LaunchProviderDetailPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/rockets/:id',
    element: <RocketPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/payloads/天舟六号货运飞船',
    element: <Tianzhou6Page />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/space-situation',
    element: <SpaceSituationPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/entity-overview',
    element: <EntityOverviewPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/settings/profile',
    element: <ProfilePage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/settings/follows',
    element: <FollowsPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/settings/keywords',
    element: <KeywordsPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/settings/membership',
    element: <MembershipPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/keyword-news/:keyword',
    element: <KeywordNewsPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/reminders',
    element: <RemindersPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/alerts',
    element: <AlertsPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/test/entity-parser',
    element: <EntityParserTest />,
    errorElement: <ErrorBoundary />
  }
]);