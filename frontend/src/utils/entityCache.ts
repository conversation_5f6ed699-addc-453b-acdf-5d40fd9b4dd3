/**
 * 实体数据缓存管理器
 * 负责缓存卫星、星座、火箭、发射服务商、发射场名称数据
 */

import { EntityCacheData, EntityDataStatus } from '../types/entity';

class EntityCache {
  private readonly STORAGE_KEY = 'space-entity-cache';
  private memoryCache: EntityCacheData | null = null;

  /**
   * 保存实体数据到本地存储
   */
  async saveEntityData(data: EntityCacheData): Promise<void> {
    try {
      // 保存到内存缓存
      this.memoryCache = data;
      
      // 保存到localStorage
      const jsonData = JSON.stringify(data);
      localStorage.setItem(this.STORAGE_KEY, jsonData);
      
      console.log('✅ 实体数据缓存保存成功');
    } catch (error) {
      console.error('❌ 实体数据缓存保存失败:', error);
      throw error;
    }
  }

  /**
   * 从本地存储读取实体数据
   */
  async getEntityData(): Promise<EntityCacheData | null> {
    try {
      // 优先从内存缓存读取
      if (this.memoryCache) {
        console.log('✅ 从内存缓存读取实体数据');
        return this.memoryCache;
      }

      // 从localStorage读取
      const jsonData = localStorage.getItem(this.STORAGE_KEY);
      if (jsonData) {
        const data = JSON.parse(jsonData) as EntityCacheData;
        // 同时放入内存缓存
        this.memoryCache = data;
        console.log('✅ 从localStorage读取实体数据');
        return data;
      }

      console.log('⚠️ 没有找到实体数据缓存');
      return null;
    } catch (error) {
      console.error('❌ 读取实体数据缓存失败:', error);
      return null;
    }
  }

  /**
   * 获取数据状态
   */
  getDataStatus(cachedData?: EntityCacheData | null): EntityDataStatus {
    const data = cachedData || this.memoryCache;
    
    if (!data) {
      return {
        hasData: false,
        isExpired: true,
        lastUpdated: null,
        nextUpdate: null,
        isUpdating: false,
        lastError: null,
        entityCounts: {
          satellites: 0,
          constellations: 0,
          rockets: 0,
          providers: 0,
          launchSites: 0
        }
      };
    }

    const now = new Date();
    const lastUpdated = new Date(data.lastUpdated);
    const nextUpdate = new Date(data.nextUpdate);
    const isExpired = now > nextUpdate;

    return {
      hasData: true,
      isExpired,
      lastUpdated: data.lastUpdated,
      nextUpdate: data.nextUpdate,
      isUpdating: false,
      lastError: null,
      entityCounts: {
        satellites: data.satellites.names.length,
        constellations: data.constellations.constellationNames.length,
        rockets: data.rockets.data.length,
        providers: data.providers.data.length,
        launchSites: data.launchSites.names.length
      }
    };
  }

  /**
   * 清除缓存数据
   */
  async clearCache(): Promise<void> {
    try {
      this.memoryCache = null;
      localStorage.removeItem(this.STORAGE_KEY);
      console.log('🗑️ 实体数据缓存已清除');
    } catch (error) {
      console.error('❌ 清除实体数据缓存失败:', error);
    }
  }

  /**
   * 检查特定实体名称是否存在
   */
  hasEntity(entityType: string, name: string): boolean {
    if (!this.memoryCache) {
      return false;
    }

    const normalizedName = name.toLowerCase().trim();

    switch (entityType) {
      case 'satellite':
        return this.memoryCache.satellites.names.some(
          n => n.toLowerCase().trim() === normalizedName
        );
      case 'constellation':
        return this.memoryCache.constellations.constellationNames.some(
          n => n.toLowerCase().trim() === normalizedName
        );
      case 'rocket':
        return this.memoryCache.rockets.data.some(
          n => n.toLowerCase().trim() === normalizedName
        );
      case 'provider':
        return this.memoryCache.providers.data.some(
          n => n.toLowerCase().trim() === normalizedName
        );
      case 'launchSite':
        return this.memoryCache.launchSites.names.some(
          n => n.toLowerCase().trim() === normalizedName
        );
      default:
        return false;
    }
  }

  /**
   * 获取所有实体名称（用于内容解析）
   */
  getAllEntityNames(): {
    satellites: string[];
    constellations: string[];
    rockets: string[];
    providers: string[];
    launchSites: string[];
  } {
    if (!this.memoryCache) {
      return {
        satellites: [],
        constellations: [],
        rockets: [],
        providers: [],
        launchSites: []
      };
    }

    return {
      satellites: this.memoryCache.satellites.names,
      constellations: this.memoryCache.constellations.constellationNames,
      rockets: this.memoryCache.rockets.data,
      providers: this.memoryCache.providers.data,
      launchSites: this.memoryCache.launchSites.names
    };
  }
}

// 导出单例实例
export const entityCache = new EntityCache();
