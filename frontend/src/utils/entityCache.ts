/**
 * 实体数据缓存管理器
 * 负责缓存卫星、星座、火箭、发射服务商、发射场名称数据
 */

import { EntityCacheData, EntityDataStatus } from '../types/entity';

class EntityCache {
  private readonly STORAGE_KEY = 'space-entity-cache';
  private memoryCache: EntityCacheData | null = null;

  /**
   * 保存实体数据到本地存储
   */
  async saveEntityData(data: EntityCacheData): Promise<void> {
    try {
      // 保存到内存缓存
      this.memoryCache = data;
      
      // 保存到localStorage
      const jsonData = JSON.stringify(data);
      localStorage.setItem(this.STORAGE_KEY, jsonData);
      
      console.log('✅ 实体数据缓存保存成功');
    } catch (error) {
      console.error('❌ 实体数据缓存保存失败:', error);
      throw error;
    }
  }

  /**
   * 从本地存储读取实体数据
   */
  async getEntityData(): Promise<EntityCacheData | null> {
    try {
      // 优先从内存缓存读取
      if (this.memoryCache) {
        console.log('✅ 从内存缓存读取实体数据');
        return this.memoryCache;
      }

      // 从localStorage读取
      const jsonData = localStorage.getItem(this.STORAGE_KEY);
      if (jsonData) {
        const data = JSON.parse(jsonData) as EntityCacheData;
        // 同时放入内存缓存
        this.memoryCache = data;
        console.log('✅ 从localStorage读取实体数据');
        return data;
      }

      console.log('⚠️ 没有找到实体数据缓存');
      return null;
    } catch (error) {
      console.error('❌ 读取实体数据缓存失败:', error);
      return null;
    }
  }

  /**
   * 获取数据状态
   */
  getDataStatus(cachedData?: EntityCacheData | null): EntityDataStatus {
    const data = cachedData || this.memoryCache;
    
    if (!data) {
      return {
        hasData: false,
        isExpired: true,
        lastUpdated: null,
        nextUpdate: null,
        isUpdating: false,
        lastError: null,
        entityCounts: {
          satellites: 0,
          constellations: 0,
          rockets: 0,
          providers: 0,
          launchSites: 0
        }
      };
    }

    const now = new Date();
    const lastUpdated = new Date(data.lastUpdated);
    const nextUpdate = new Date(data.nextUpdate);
    const isExpired = now > nextUpdate;

    return {
      hasData: true,
      isExpired,
      lastUpdated: data.lastUpdated,
      nextUpdate: data.nextUpdate,
      isUpdating: false,
      lastError: null,
      entityCounts: {
        satellites: data.satellites.names.length,
        constellations: data.constellations.constellationNames.length,
        rockets: data.rockets.data.length,
        providers: data.providers.data.length,
        launchSites: data.launchSites.names.length
      }
    };
  }

  /**
   * 清除缓存数据
   */
  async clearCache(): Promise<void> {
    try {
      this.memoryCache = null;
      localStorage.removeItem(this.STORAGE_KEY);
      console.log('🗑️ 实体数据缓存已清除');
    } catch (error) {
      console.error('❌ 清除实体数据缓存失败:', error);
    }
  }

  /**
   * 检查特定实体名称是否存在
   */
  hasEntity(entityType: string, name: string): boolean {
    if (!this.memoryCache) {
      return false;
    }

    const normalizedName = name.toLowerCase().trim();

    switch (entityType) {
      case 'satellite':
        return this.memoryCache.satellites.names.some(
          n => n.toLowerCase().trim() === normalizedName
        );
      case 'constellation':
        return this.memoryCache.constellations.constellationNames.some(
          n => n.toLowerCase().trim() === normalizedName
        );
      case 'rocket':
        return this.memoryCache.rockets.data.some(
          n => n.toLowerCase().trim() === normalizedName
        );
      case 'provider':
        return this.memoryCache.providers.data.some(
          n => n.toLowerCase().trim() === normalizedName
        );
      case 'launchSite':
        return this.memoryCache.launchSites.names.some(
          n => n.toLowerCase().trim() === normalizedName
        );
      default:
        return false;
    }
  }

  /**
   * 获取实体的翻译名称
   */
  private getEntityTranslations(): {
    satellites: string[];
    constellations: string[];
    rockets: string[];
    providers: string[];
    launchSites: string[];
  } {
    // 火箭翻译
    const rocketTranslations: Record<string, string[]> = {
      'Falcon 9': ['猎鹰9号', '猎鹰九号', '猎鹰9'],
      'Falcon Heavy': ['猎鹰重型', '猎鹰重型火箭'],
      'Long March': ['长征'],
      'Atlas V': ['宇宙神5号', '宇宙神V'],
      'Delta IV': ['德尔塔4号', '德尔塔IV'],
      'Ariane 5': ['阿丽亚娜5号', '阿里安5号'],
      'Soyuz': ['联盟号', '联盟'],
      'Proton': ['质子号', '质子火箭']
    };

    // 发射场翻译
    const launchSiteTranslations: Record<string, string[]> = {
      'Kennedy Space Center': ['肯尼迪航天中心', 'KSC'],
      'Cape Canaveral Space Force Station': ['卡纳维拉尔角航天发射场', '卡纳维拉尔角', '卡纳维拉尔角太空军基地'],
      'Vandenberg Space Force Base': ['范登堡空军基地', '范登堡太空军基地'],
      'Baikonur Cosmodrome': ['拜科努尔航天发射场', '拜科努尔'],
      'Space Launch Complex 40': ['太空发射综合体40', '40号发射台', '发射台40'],
      'Launch Complex 39A': ['39A发射台', '发射台39A']
    };

    // 发射服务商翻译
    const providerTranslations: Record<string, string[]> = {
      'SpaceX': ['太空探索技术公司', 'Space Exploration Technologies'],
      'NASA': ['美国国家航空航天局', '美国宇航局'],
      'ESA': ['欧洲航天局', '欧空局'],
      'Roscosmos': ['俄罗斯航天局', '俄罗斯联邦航天局'],
      'CNSA': ['中国国家航天局', '国家航天局'],
      'JAXA': ['日本宇宙航空研究开发机构', '日本航天局'],
      'ASTSpaceMobile': ['AST太空移动', 'AST SpaceMobile']
    };

    // 卫星/星座翻译
    const satelliteTranslations: Record<string, string[]> = {
      'Starlink': ['星链', '星链卫星'],
      'Starlink V2 Mini': ['星链V2 Mini', '星链V2迷你版'],
      'Starlink 12-26': ['星链12-26', '星链12-26任务'],
      'Weather': ['气象', '天气'],
      'Earth': ['地球', '近地轨道'],
      'Crew-6': ['载人-6'],
      'Bluebird': ['蓝鸟', 'Bluebird 1-5'],
      'USSF-124': ['美国太空军-124'],
      'Weather Squadron': ['气象中队', '45气象中队'],
      'DTC': ['直接到蜂窝', '直连蜂窝'],
      'B1078': ['B1078', '编号B1078']
    };

    return {
      satellites: Object.values(satelliteTranslations).flat(),
      constellations: Object.values(satelliteTranslations).flat(),
      rockets: Object.values(rocketTranslations).flat(),
      providers: Object.values(providerTranslations).flat(),
      launchSites: Object.values(launchSiteTranslations).flat()
    };
  }

  /**
   * 获取所有实体名称（用于内容解析）
   */
  getAllEntityNames(): {
    satellites: string[];
    constellations: string[];
    rockets: string[];
    providers: string[];
    launchSites: string[];
  } {
    if (!this.memoryCache) {
      return {
        satellites: [],
        constellations: [],
        rockets: [],
        providers: [],
        launchSites: []
      };
    }

    const translations = this.getEntityTranslations();

    return {
      satellites: [...this.memoryCache.satellites.names, ...translations.satellites],
      constellations: [...this.memoryCache.constellations.constellationNames, ...translations.constellations],
      rockets: [...this.memoryCache.rockets.data, ...translations.rockets],
      providers: [...this.memoryCache.providers.data, ...translations.providers],
      launchSites: [...this.memoryCache.launchSites.names, ...translations.launchSites]
    };
  }
}

// 导出单例实例
export const entityCache = new EntityCache();
