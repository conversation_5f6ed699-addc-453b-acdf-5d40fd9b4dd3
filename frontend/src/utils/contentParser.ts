import { entityCache } from './entityCache';
import { EntityType, EntityMatch } from '../types/entity';

/**
 * 获取实体对应的URL
 */
function getEntityUrl(type: EntityType, name: string): string {
  const encodedName = encodeURIComponent(name);

  switch (type) {
    case EntityType.SATELLITE:
      return `/satellite/${encodedName}`;
    case EntityType.CONSTELLATION:
      return `/constellation/${encodedName}`;
    case EntityType.ROCKET:
      return `/rockets/${encodedName}`;
    case EntityType.PROVIDER:
      return `/launch-provider/${encodedName}`;
    case EntityType.LAUNCH_SITE:
      return `/launch-site/${encodedName}`;
    default:
      return '#';
  }
}

/**
 * 创建正则表达式来匹配实体名称
 * 支持中英文名称，考虑标点符号边界
 */
function createEntityRegex(names: string[]): RegExp {
  if (names.length === 0) return /(?!)/; // 永远不匹配的正则

  // 按长度排序，长的在前，避免短名称覆盖长名称
  const sortedNames = names.sort((a, b) => b.length - a.length);

  // 转义特殊字符并创建正则表达式
  const escapedNames = sortedNames.map(name =>
    name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  );

  // 简化的边界匹配，使用更简单的方式
  const pattern = `(${escapedNames.join('|')})`;

  return new RegExp(pattern, 'gi');
}

/**
 * 在文本中查找所有实体
 */
function findEntitiesInText(text: string): EntityMatch[] {
  const entities: EntityMatch[] = [];
  const entityNames = entityCache.getAllEntityNames();

  // 检查卫星名称
  const satelliteRegex = createEntityRegex(entityNames.satellites);
  let match;
  while ((match = satelliteRegex.exec(text)) !== null) {
    const name = match[1];
    entities.push({
      type: EntityType.SATELLITE,
      name,
      displayName: name,
      url: getEntityUrl(EntityType.SATELLITE, name),
      startIndex: match.index,
      endIndex: match.index + name.length
    });
  }

  // 检查星座名称
  const constellationRegex = createEntityRegex(entityNames.constellations);
  constellationRegex.lastIndex = 0;
  while ((match = constellationRegex.exec(text)) !== null) {
    const name = match[1];
    entities.push({
      type: EntityType.CONSTELLATION,
      name,
      displayName: name,
      url: getEntityUrl(EntityType.CONSTELLATION, name),
      startIndex: match.index,
      endIndex: match.index + name.length
    });
  }

  // 检查火箭型号
  const rocketRegex = createEntityRegex(entityNames.rockets);
  rocketRegex.lastIndex = 0;
  while ((match = rocketRegex.exec(text)) !== null) {
    const name = match[1];
    entities.push({
      type: EntityType.ROCKET,
      name,
      displayName: name,
      url: getEntityUrl(EntityType.ROCKET, name),
      startIndex: match.index,
      endIndex: match.index + name.length
    });
  }

  // 检查发射服务商
  const providerRegex = createEntityRegex(entityNames.providers);
  providerRegex.lastIndex = 0;
  while ((match = providerRegex.exec(text)) !== null) {
    const name = match[1];
    entities.push({
      type: EntityType.PROVIDER,
      name,
      displayName: name,
      url: getEntityUrl(EntityType.PROVIDER, name),
      startIndex: match.index,
      endIndex: match.index + name.length
    });
  }

  // 检查发射场名称
  const launchSiteRegex = createEntityRegex(entityNames.launchSites);
  launchSiteRegex.lastIndex = 0;
  while ((match = launchSiteRegex.exec(text)) !== null) {
    const name = match[1];
    entities.push({
      type: EntityType.LAUNCH_SITE,
      name,
      displayName: name,
      url: getEntityUrl(EntityType.LAUNCH_SITE, name),
      startIndex: match.index,
      endIndex: match.index + name.length
    });
  }

  return entities;
}

/**
 * 解决实体重叠问题
 * 如果两个实体有重叠，保留较长的实体
 */
function resolveEntityOverlaps(entities: EntityMatch[]): EntityMatch[] {
  // 按开始位置排序
  entities.sort((a, b) => a.startIndex - b.startIndex);

  const resolvedEntities: EntityMatch[] = [];

  for (const entity of entities) {
    // 检查是否与已添加的实体重叠
    const hasOverlap = resolvedEntities.some(existing =>
      (entity.startIndex < existing.endIndex && entity.endIndex > existing.startIndex)
    );

    if (!hasOverlap) {
      resolvedEntities.push(entity);
    } else {
      // 如果有重叠，比较长度，保留较长的实体
      const overlappingIndex = resolvedEntities.findIndex(existing =>
        (entity.startIndex < existing.endIndex && entity.endIndex > existing.startIndex)
      );

      if (overlappingIndex !== -1) {
        const existing = resolvedEntities[overlappingIndex];
        const entityLength = entity.endIndex - entity.startIndex;
        const existingLength = existing.endIndex - existing.startIndex;

        if (entityLength > existingLength) {
          resolvedEntities[overlappingIndex] = entity;
        }
      }
    }
  }

  return resolvedEntities;
}

/**
 * 将实体转换为HTML链接
 */
function entitiesToHtml(text: string, entities: EntityMatch[]): string {
  if (entities.length === 0) {
    return text;
  }

  // 解决重叠问题
  const resolvedEntities = resolveEntityOverlaps(entities);

  // 按开始位置倒序排序，从后往前替换，避免位置偏移
  resolvedEntities.sort((a, b) => b.startIndex - a.startIndex);

  let result = text;

  for (const entity of resolvedEntities) {
    const before = result.substring(0, entity.startIndex);
    const after = result.substring(entity.endIndex);
    const linkHtml = `<a href="${entity.url}" class="text-blue-400 hover:text-blue-300 transition-colors underline" title="点击查看${entity.displayName}详情">${entity.displayName}</a>`;

    result = before + linkHtml + after;
  }

  return result;
}

/**
 * 解析内容并为实体添加链接
 * 这是主要的导出函数
 */
export function parseContent(content: string): string {
  if (!content || content.trim() === '') {
    return content;
  }

  try {
    // 查找所有实体
    const entities = findEntitiesInText(content);

    if (entities.length === 0) {
      return content;
    }

    // 转换为HTML
    return entitiesToHtml(content, entities);

  } catch (error) {
    console.error('解析内容时发生错误:', error);
    // 如果解析失败，返回原始内容
    return content;
  }
}

/**
 * 检查实体数据是否可用
 */
export function isEntityDataAvailable(): boolean {
  const entityNames = entityCache.getAllEntityNames();
  return (
    entityNames.satellites.length > 0 ||
    entityNames.constellations.length > 0 ||
    entityNames.rockets.length > 0 ||
    entityNames.providers.length > 0 ||
    entityNames.launchSites.length > 0
  );
}

/**
 * 获取实体统计信息
 */
export function getEntityStats(): {
  satellites: number;
  constellations: number;
  rockets: number;
  providers: number;
  launchSites: number;
  total: number;
} {
  const entityNames = entityCache.getAllEntityNames();

  const stats = {
    satellites: entityNames.satellites.length,
    constellations: entityNames.constellations.length,
    rockets: entityNames.rockets.length,
    providers: entityNames.providers.length,
    launchSites: entityNames.launchSites.length,
    total: 0
  };

  stats.total = stats.satellites + stats.constellations + stats.rockets + stats.providers + stats.launchSites;

  return stats;
}