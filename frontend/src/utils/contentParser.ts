import { entityCache } from './entityCache';
import { EntityType, EntityMatch } from '../types/entity';

/**
 * 获取实体对应的URL
 */
function getEntityUrl(type: EntityType, name: string): string {
  const encodedName = encodeURIComponent(name);

  switch (type) {
    case EntityType.SATELLITE:
      return `/satellite/${encodedName}`;
    case EntityType.CONSTELLATION:
      return `/constellation/${encodedName}`;
    case EntityType.ROCKET:
      return `/rockets/${encodedName}`;
    case EntityType.PROVIDER:
      return `/launch-provider/${encodedName}`;
    case EntityType.LAUNCH_SITE:
      return `/launch-site/${encodedName}`;
    default:
      return '#';
  }
}

/**
 * 检查字符是否为单词边界字符
 */
function isWordBoundary(char: string): boolean {
  if (!char) return true;
  // 英文单词边界：空格、标点符号
  // 中文单词边界：中文标点符号、空格
  return /[\s\p{P}\p{Z}]/u.test(char) || /[\u3000-\u303F\uFF00-\uFFEF]/.test(char);
}

/**
 * 检查位置是否为有效的单词边界
 */
function isValidWordBoundary(text: string, startIndex: number, endIndex: number): boolean {
  const beforeChar = startIndex > 0 ? text[startIndex - 1] : '';
  const afterChar = endIndex < text.length ? text[endIndex] : '';

  return isWordBoundary(beforeChar) && isWordBoundary(afterChar);
}

/**
 * 创建实体名称映射表，支持中英文对应关系
 */
function createEntityNameMap(names: string[]): Map<string, string> {
  const nameMap = new Map<string, string>();

  names.forEach(name => {
    // 原名称映射到自己
    nameMap.set(name.toLowerCase().trim(), name);

    // 添加一些常见的中英文对应关系
    const translations = getEntityTranslations(name);
    translations.forEach(translation => {
      nameMap.set(translation.toLowerCase().trim(), name);
    });
  });

  return nameMap;
}

/**
 * 获取实体的翻译对应关系
 */
function getEntityTranslations(entityName: string): string[] {
  const translations: string[] = [];

  // 火箭翻译对应关系
  const rocketTranslations: Record<string, string[]> = {
    'Falcon 9': ['猎鹰9号', '猎鹰九号', 'Falcon9'],
    'Falcon Heavy': ['猎鹰重型', '猎鹰重型火箭'],
    'Long March': ['长征'],
    'Atlas V': ['宇宙神5号', '宇宙神V'],
    'Delta IV': ['德尔塔4号', '德尔塔IV'],
    'Ariane 5': ['阿丽亚娜5号', '阿里安5号'],
    'Soyuz': ['联盟号', '联盟'],
    'Proton': ['质子号', '质子火箭']
  };

  // 发射场翻译对应关系
  const launchSiteTranslations: Record<string, string[]> = {
    'Kennedy Space Center': ['肯尼迪航天中心', 'KSC'],
    'Cape Canaveral Space Force Station': ['卡纳维拉尔角航天发射场', '卡纳维拉尔角'],
    'Vandenberg Space Force Base': ['范登堡空军基地', '范登堡太空军基地'],
    'Baikonur Cosmodrome': ['拜科努尔航天发射场', '拜科努尔'],
    'Jiuquan Satellite Launch Center': ['酒泉卫星发射中心', '酒泉发射场'],
    'Xichang Satellite Launch Center': ['西昌卫星发射中心', '西昌发射场'],
    'Taiyuan Satellite Launch Center': ['太原卫星发射中心', '太原发射场'],
    'Wenchang Spacecraft Launch Site': ['文昌航天发射场', '文昌发射场']
  };

  // 发射服务商翻译对应关系
  const providerTranslations: Record<string, string[]> = {
    'SpaceX': ['太空探索技术公司', 'Space Exploration Technologies'],
    'NASA': ['美国国家航空航天局', '美国宇航局'],
    'ESA': ['欧洲航天局', '欧空局'],
    'Roscosmos': ['俄罗斯航天局', '俄罗斯联邦航天局'],
    'CNSA': ['中国国家航天局', '国家航天局'],
    'JAXA': ['日本宇宙航空研究开发机构', '日本航天局']
  };

  // 查找对应的翻译
  Object.entries(rocketTranslations).forEach(([key, values]) => {
    if (key.toLowerCase() === entityName.toLowerCase() ||
        values.some(v => v.toLowerCase() === entityName.toLowerCase())) {
      translations.push(key, ...values);
    }
  });

  Object.entries(launchSiteTranslations).forEach(([key, values]) => {
    if (key.toLowerCase() === entityName.toLowerCase() ||
        values.some(v => v.toLowerCase() === entityName.toLowerCase())) {
      translations.push(key, ...values);
    }
  });

  Object.entries(providerTranslations).forEach(([key, values]) => {
    if (key.toLowerCase() === entityName.toLowerCase() ||
        values.some(v => v.toLowerCase() === entityName.toLowerCase())) {
      translations.push(key, ...values);
    }
  });

  // 去重并过滤掉原名称
  return [...new Set(translations)].filter(t =>
    t.toLowerCase() !== entityName.toLowerCase()
  );
}

/**
 * 在文本中查找特定类型的实体
 */
function findEntitiesByType(
  text: string,
  entityNames: string[],
  entityType: EntityType
): EntityMatch[] {
  const entities: EntityMatch[] = [];
  const nameMap = createEntityNameMap(entityNames);

  // 按长度排序，长的在前，避免短名称覆盖长名称
  const sortedNames = Array.from(nameMap.keys()).sort((a, b) => b.length - a.length);

  for (const searchName of sortedNames) {
    const originalName = nameMap.get(searchName)!;
    const escapedName = searchName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(escapedName, 'gi');

    let match;
    while ((match = regex.exec(text)) !== null) {
      const startIndex = match.index;
      const endIndex = startIndex + match[0].length;

      // 检查是否为完整单词（单词边界检查）
      if (!isValidWordBoundary(text, startIndex, endIndex)) {
        continue;
      }

      // 检查是否与已有实体重叠
      const hasOverlap = entities.some(existing =>
        (startIndex < existing.endIndex && endIndex > existing.startIndex)
      );

      if (!hasOverlap) {
        entities.push({
          type: entityType,
          name: originalName,
          displayName: match[0], // 使用实际匹配的文本作为显示名称
          url: getEntityUrl(entityType, originalName),
          startIndex,
          endIndex
        });
      }
    }

    // 重置正则表达式的lastIndex
    regex.lastIndex = 0;
  }

  return entities;
}

/**
 * 在文本中查找所有实体
 */
function findEntitiesInText(text: string): EntityMatch[] {
  const entities: EntityMatch[] = [];
  const entityNames = entityCache.getAllEntityNames();

  // 查找各类型实体
  entities.push(...findEntitiesByType(text, entityNames.satellites, EntityType.SATELLITE));
  entities.push(...findEntitiesByType(text, entityNames.constellations, EntityType.CONSTELLATION));
  entities.push(...findEntitiesByType(text, entityNames.rockets, EntityType.ROCKET));
  entities.push(...findEntitiesByType(text, entityNames.providers, EntityType.PROVIDER));
  entities.push(...findEntitiesByType(text, entityNames.launchSites, EntityType.LAUNCH_SITE));

  return entities;
}

/**
 * 解决实体重叠问题
 * 优先级：长度 > 实体类型优先级 > 位置
 */
function resolveEntityOverlaps(entities: EntityMatch[]): EntityMatch[] {
  if (entities.length === 0) return [];

  // 实体类型优先级（数字越小优先级越高）
  const typePriority: Record<EntityType, number> = {
    [EntityType.ROCKET]: 1,
    [EntityType.CONSTELLATION]: 2,
    [EntityType.SATELLITE]: 3,
    [EntityType.LAUNCH_SITE]: 4,
    [EntityType.PROVIDER]: 5
  };

  // 按优先级排序：长度（降序）> 类型优先级（升序）> 位置（升序）
  entities.sort((a, b) => {
    const lengthA = a.endIndex - a.startIndex;
    const lengthB = b.endIndex - b.startIndex;

    if (lengthA !== lengthB) {
      return lengthB - lengthA; // 长度降序
    }

    const priorityA = typePriority[a.type] || 999;
    const priorityB = typePriority[b.type] || 999;

    if (priorityA !== priorityB) {
      return priorityA - priorityB; // 优先级升序
    }

    return a.startIndex - b.startIndex; // 位置升序
  });

  const resolvedEntities: EntityMatch[] = [];

  for (const entity of entities) {
    // 检查是否与已添加的实体重叠
    const hasOverlap = resolvedEntities.some(existing =>
      (entity.startIndex < existing.endIndex && entity.endIndex > existing.startIndex)
    );

    if (!hasOverlap) {
      resolvedEntities.push(entity);
    }
  }

  // 按位置重新排序
  return resolvedEntities.sort((a, b) => a.startIndex - b.startIndex);
}

/**
 * 将实体转换为HTML链接
 */
function entitiesToHtml(text: string, entities: EntityMatch[]): string {
  if (entities.length === 0) {
    return text;
  }

  // 解决重叠问题
  const resolvedEntities = resolveEntityOverlaps(entities);

  // 按开始位置倒序排序，从后往前替换，避免位置偏移
  resolvedEntities.sort((a, b) => b.startIndex - a.startIndex);

  let result = text;

  for (const entity of resolvedEntities) {
    const before = result.substring(0, entity.startIndex);
    const after = result.substring(entity.endIndex);
    const linkHtml = `<a href="${entity.url}" class="text-blue-400 hover:text-blue-300 transition-colors underline" title="点击查看${entity.displayName}详情">${entity.displayName}</a>`;

    result = before + linkHtml + after;
  }

  return result;
}

/**
 * 解析内容并为实体添加链接
 * 这是主要的导出函数
 */
export function parseContent(content: string): string {
  if (!content || content.trim() === '') {
    return content;
  }

  try {
    // 查找所有实体
    const entities = findEntitiesInText(content);

    if (entities.length === 0) {
      return content;
    }

    // 转换为HTML
    return entitiesToHtml(content, entities);

  } catch (error) {
    console.error('解析内容时发生错误:', error);
    // 如果解析失败，返回原始内容
    return content;
  }
}

/**
 * 解析双语内容，确保中英文版本的实体高亮一致性
 */
export function parseBilingualContent(
  originalContent: string,
  translatedContent: string
): { original: string; translated: string } {
  if (!originalContent && !translatedContent) {
    return { original: '', translated: '' };
  }

  try {
    // 分别解析两个版本的内容
    const originalParsed = originalContent ? parseContent(originalContent) : '';
    const translatedParsed = translatedContent ? parseContent(translatedContent) : '';

    return {
      original: originalParsed,
      translated: translatedParsed
    };

  } catch (error) {
    console.error('解析双语内容时发生错误:', error);
    return {
      original: originalContent || '',
      translated: translatedContent || ''
    };
  }
}

/**
 * 验证实体匹配的准确性
 */
export function validateEntityMatch(text: string, startIndex: number, endIndex: number): boolean {
  if (startIndex < 0 || endIndex > text.length || startIndex >= endIndex) {
    return false;
  }

  return isValidWordBoundary(text, startIndex, endIndex);
}

/**
 * 检查实体数据是否可用
 */
export function isEntityDataAvailable(): boolean {
  const entityNames = entityCache.getAllEntityNames();
  return (
    entityNames.satellites.length > 0 ||
    entityNames.constellations.length > 0 ||
    entityNames.rockets.length > 0 ||
    entityNames.providers.length > 0 ||
    entityNames.launchSites.length > 0
  );
}

/**
 * 获取实体统计信息
 */
export function getEntityStats(): {
  satellites: number;
  constellations: number;
  rockets: number;
  providers: number;
  launchSites: number;
  total: number;
} {
  const entityNames = entityCache.getAllEntityNames();

  const stats = {
    satellites: entityNames.satellites.length,
    constellations: entityNames.constellations.length,
    rockets: entityNames.rockets.length,
    providers: entityNames.providers.length,
    launchSites: entityNames.launchSites.length,
    total: 0
  };

  stats.total = stats.satellites + stats.constellations + stats.rockets + stats.providers + stats.launchSites;

  return stats;
}

/**
 * 调试函数：分析文本中的实体匹配情况
 */
export function debugEntityMatching(text: string): {
  entities: EntityMatch[];
  analysis: {
    totalMatches: number;
    byType: Record<string, number>;
    overlaps: number;
    invalidBoundaries: number;
  };
} {
  const entities = findEntitiesInText(text);
  const resolvedEntities = resolveEntityOverlaps(entities);

  const byType: Record<string, number> = {};
  entities.forEach(entity => {
    byType[entity.type] = (byType[entity.type] || 0) + 1;
  });

  const overlaps = entities.length - resolvedEntities.length;

  let invalidBoundaries = 0;
  entities.forEach(entity => {
    if (!validateEntityMatch(text, entity.startIndex, entity.endIndex)) {
      invalidBoundaries++;
    }
  });

  return {
    entities: resolvedEntities,
    analysis: {
      totalMatches: entities.length,
      byType,
      overlaps,
      invalidBoundaries
    }
  };
}

/**
 * 获取支持的实体翻译对照表
 */
export function getSupportedTranslations(): Record<string, string[]> {
  return {
    'Falcon 9': ['猎鹰9号', '猎鹰九号'],
    'Falcon Heavy': ['猎鹰重型', '猎鹰重型火箭'],
    'SpaceX': ['太空探索技术公司'],
    'Kennedy Space Center': ['肯尼迪航天中心'],
    'Cape Canaveral Space Force Station': ['卡纳维拉尔角航天发射场'],
    'Starlink': ['星链', '星链卫星']
  };
}