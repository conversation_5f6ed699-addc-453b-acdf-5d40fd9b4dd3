/**
 * 实体数据管理服务
 * 负责定时请求API获取实体名称数据，并管理本地数据缓存
 */

import { apiService } from './apiService';
import { entityCache } from '../utils/entityCache';
import { 
  EntityCacheData, 
  EntityDataStatus,
  SatelliteNameData,
  ConstellationNameData,
  RocketNameData,
  LaunchProviderData,
  LaunchSiteNameData
} from '../types/entity';

class EntityDataManager {
  private updateInterval: NodeJS.Timeout | null = null;
  private readonly UPDATE_INTERVAL_MS = 24 * 60 * 60 * 1000; // 24小时
  private readonly RETRY_INTERVAL_MS = 60 * 60 * 1000; // 1小时重试间隔
  private readonly MAX_RETRIES = 3; // 最大重试次数
  
  private isUpdating = false;
  private lastError: string | null = null;
  private retryCount = 0;
  private retryTimeout: NodeJS.Timeout | null = null;

  // 事件监听器
  private listeners: {
    onUpdateStart?: () => void;
    onUpdateProgress?: (progress: number, message: string) => void;
    onUpdateComplete?: (data: EntityCacheData) => void;
    onUpdateError?: (error: string) => void;
    onStatusChange?: (status: EntityDataStatus) => void;
  } = {};

  /**
   * 初始化实体数据管理器
   */
  async init(): Promise<void> {
    console.log('🚀 初始化实体数据管理器');
    
    try {
      // 检查本地数据状态
      const cachedData = await entityCache.getEntityData();
      const status = this.getDataStatus(cachedData);
      
      console.log('📊 实体数据状态:', status);
      
      // 如果没有数据或数据过期，立即获取一次
      if (!status.hasData || status.isExpired) {
        console.log('📡 本地数据不存在或已过期，立即获取实体数据');
        await this.updateEntityData();
      } else {
        console.log('✅ 本地数据有效，使用缓存数据');
      }
      
      // 启动定时更新
      this.startPeriodicUpdate();
      
      console.log('🎉 实体数据管理器初始化完成');
    } catch (error) {
      console.error('❌ 实体数据管理器初始化失败:', error);
      this.lastError = error instanceof Error ? error.message : String(error);
      this.notifyStatusChange();
    }
  }

  /**
   * 启动定时更新
   */
  private startPeriodicUpdate(): void {
    // 清除现有定时器
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }

    // 设置新的定时器
    this.updateInterval = setInterval(async () => {
      console.log('⏰ 定时更新实体数据');
      await this.updateEntityData();
    }, this.UPDATE_INTERVAL_MS);

    console.log(`⏱️ 已设置定时更新，间隔：${this.UPDATE_INTERVAL_MS / 1000 / 60 / 60}小时`);
  }

  /**
   * 更新实体数据
   */
  async updateEntityData(force: boolean = false): Promise<EntityCacheData | null> {
    if (this.isUpdating && !force) {
      console.log('⚠️ 正在更新中，跳过本次更新');
      return null;
    }

    console.log('🚀 updateEntityData 开始执行，force =', force);
    this.isUpdating = true;
    this.lastError = null;
    this.notifyUpdateStart();
    this.notifyStatusChange();

    try {
      const now = new Date();
      const nextUpdate = new Date(now.getTime() + this.UPDATE_INTERVAL_MS);

      // 并行获取所有实体数据
      this.notifyUpdateProgress(10, '正在获取卫星名称...');
      const satelliteData = await this.fetchSatelliteNames();

      this.notifyUpdateProgress(30, '正在获取星座名称...');
      const constellationData = await this.fetchConstellationNames();

      this.notifyUpdateProgress(50, '正在获取火箭型号...');
      const rocketData = await this.fetchRocketNames();

      this.notifyUpdateProgress(70, '正在获取发射服务商...');
      const providerData = await this.fetchProviderNames();

      this.notifyUpdateProgress(90, '正在获取发射场名称...');
      const launchSiteData = await this.fetchLaunchSiteNames();

      // 准备缓存数据
      const cacheData: EntityCacheData = {
        id: 'entity-cache',
        lastUpdated: now.toISOString(),
        nextUpdate: nextUpdate.toISOString(),
        satellites: satelliteData,
        constellations: constellationData,
        rockets: rocketData,
        providers: providerData,
        launchSites: launchSiteData
      };

      // 保存到缓存
      await entityCache.saveEntityData(cacheData);

      this.notifyUpdateProgress(100, '实体数据更新完成');
      this.notifyUpdateComplete(cacheData);
      
      // 重置重试计数
      this.retryCount = 0;
      if (this.retryTimeout) {
        clearTimeout(this.retryTimeout);
        this.retryTimeout = null;
      }

      console.log('✅ 实体数据更新成功');
      return cacheData;

    } catch (error) {
      console.error('❌ 更新实体数据失败:', error);
      this.lastError = error instanceof Error ? error.message : String(error);
      this.notifyUpdateError(this.lastError);
      
      // 重试机制
      this.scheduleRetry();
      
      return null;
    } finally {
      this.isUpdating = false;
      this.notifyStatusChange();
    }
  }

  /**
   * 获取卫星名称
   */
  private async fetchSatelliteNames(): Promise<SatelliteNameData> {
    try {
      const response = await apiService.get<string[]>('/local/satellite/names');
      return {
        names: response || [],
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('获取卫星名称失败:', error);
      return {
        names: [],
        lastUpdated: new Date().toISOString()
      };
    }
  }

  /**
   * 获取星座名称
   */
  private async fetchConstellationNames(): Promise<ConstellationNameData> {
    try {
      const response = await apiService.get<{ constellationNames: string[] }>('/constellation/names');
      return {
        constellationNames: response?.constellationNames || [],
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('获取星座名称失败:', error);
      return {
        constellationNames: [],
        lastUpdated: new Date().toISOString()
      };
    }
  }

  /**
   * 获取火箭型号
   */
  private async fetchRocketNames(): Promise<RocketNameData> {
    try {
      const response = await apiService.get<{ success: boolean; data: string[] }>('/api/es/launch/rocket-names');
      return {
        success: response?.success || false,
        data: response?.data || [],
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('获取火箭型号失败:', error);
      return {
        success: false,
        data: [],
        lastUpdated: new Date().toISOString()
      };
    }
  }

  /**
   * 获取发射服务商名称
   */
  private async fetchProviderNames(): Promise<LaunchProviderData> {
    try {
      const response = await apiService.get<{ success: boolean; data: string[] }>('/api/es/launch/providers');
      return {
        success: response?.success || false,
        data: response?.data || [],
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('获取发射服务商名称失败:', error);
      return {
        success: false,
        data: [],
        lastUpdated: new Date().toISOString()
      };
    }
  }

  /**
   * 获取发射场名称（从launchSite.ts文件中提取）
   */
  private async fetchLaunchSiteNames(): Promise<LaunchSiteNameData> {
    try {
      // 动态导入发射场数据
      const { launchSites } = await import('../data/launchSites');

      // 提取所有发射场名称（包括英文名、中文名和别名）
      const launchSiteNames: string[] = [];

      launchSites.forEach(site => {
        // 添加英文名称
        if (site.englishName) {
          launchSiteNames.push(site.englishName);
        }

        // 添加中文名称
        if (site.chineseName) {
          launchSiteNames.push(site.chineseName);
        }

        // 添加别名
        if (site.aliases && site.aliases.length > 0) {
          launchSiteNames.push(...site.aliases);
        }
      });

      // 去重并过滤空值
      const uniqueNames = [...new Set(launchSiteNames)].filter(name => name && name.trim());

      console.log(`✅ 从本地数据提取到 ${uniqueNames.length} 个发射场名称`);

      return {
        names: uniqueNames,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('获取发射场名称失败:', error);

      // 如果导入失败，返回一些基本的发射场名称
      const fallbackNames = [
        'Kennedy Space Center',
        '肯尼迪航天中心',
        'Cape Canaveral Space Force Station',
        '卡纳维拉尔角航天发射场',
        'Vandenberg Space Force Base',
        '范登堡空军基地',
        'Baikonur Cosmodrome',
        '拜科努尔航天发射场',
        'Jiuquan Satellite Launch Center',
        '酒泉卫星发射中心',
        'Xichang Satellite Launch Center',
        '西昌卫星发射中心',
        'Taiyuan Satellite Launch Center',
        '太原卫星发射中心',
        'Wenchang Spacecraft Launch Site',
        '文昌航天发射场'
      ];

      return {
        names: fallbackNames,
        lastUpdated: new Date().toISOString()
      };
    }
  }

  /**
   * 安排重试
   */
  private scheduleRetry(): void {
    if (this.retryCount >= this.MAX_RETRIES) {
      console.log(`❌ 已达到最大重试次数 (${this.MAX_RETRIES})，停止重试`);
      return;
    }

    this.retryCount++;
    const retryDelay = this.RETRY_INTERVAL_MS * Math.pow(2, this.retryCount - 1); // 指数退避

    console.log(`🔄 安排第 ${this.retryCount} 次重试，延迟 ${retryDelay / 1000 / 60} 分钟`);

    this.retryTimeout = setTimeout(async () => {
      console.log(`🔄 执行第 ${this.retryCount} 次重试`);
      await this.updateEntityData();
    }, retryDelay);
  }

  /**
   * 获取实体数据（优先使用缓存）
   */
  async getEntityData(): Promise<EntityCacheData | null> {
    try {
      console.log('🔍 开始获取实体数据（优先使用缓存）');
      const cachedData = await entityCache.getEntityData();

      if (cachedData) {
        console.log('✅ 使用缓存的实体数据');
        return cachedData;
      } else {
        console.log('⚠️ 没有有效的缓存数据，尝试立即获取');
        const freshData = await this.updateEntityData(true);
        return freshData;
      }
    } catch (error) {
      console.error('❌ 获取实体数据失败:', error);
      return null;
    }
  }

  /**
   * 获取数据状态
   */
  getDataStatus(cachedData?: EntityCacheData | null): EntityDataStatus {
    const data = cachedData || null;
    const baseStatus = entityCache.getDataStatus(data);

    return {
      ...baseStatus,
      isUpdating: this.isUpdating,
      lastError: this.lastError
    };
  }

  /**
   * 手动刷新数据
   */
  async refreshData(): Promise<EntityCacheData | null> {
    console.log('🔄 手动刷新实体数据');
    return await this.updateEntityData(true);
  }

  /**
   * 清除缓存数据
   */
  async clearCache(): Promise<void> {
    console.log('🗑️ 清除实体数据缓存');
    await entityCache.clearCache();
    this.notifyStatusChange();
  }

  /**
   * 设置事件监听器
   */
  setListeners(listeners: Partial<typeof this.listeners>): void {
    this.listeners = { ...this.listeners, ...listeners };
  }

  /**
   * 通知更新开始
   */
  private notifyUpdateStart(): void {
    if (this.listeners.onUpdateStart) {
      this.listeners.onUpdateStart();
    }
  }

  /**
   * 通知更新进度
   */
  private notifyUpdateProgress(progress: number, message: string): void {
    if (this.listeners.onUpdateProgress) {
      this.listeners.onUpdateProgress(progress, message);
    }
  }

  /**
   * 通知更新完成
   */
  private notifyUpdateComplete(data: EntityCacheData): void {
    if (this.listeners.onUpdateComplete) {
      this.listeners.onUpdateComplete(data);
    }
  }

  /**
   * 通知更新错误
   */
  private notifyUpdateError(error: string): void {
    if (this.listeners.onUpdateError) {
      this.listeners.onUpdateError(error);
    }
  }

  /**
   * 通知状态变化
   */
  private notifyStatusChange(): void {
    if (this.listeners.onStatusChange) {
      const status = this.getDataStatus();
      this.listeners.onStatusChange(status);
    }
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
      this.retryTimeout = null;
    }
    this.listeners = {};
  }
}

// 导出单例实例
export const entityDataManager = new EntityDataManager();
