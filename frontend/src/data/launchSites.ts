/**
 * 发射场数据类型定义
 */
export interface LaunchSite {
  code: string;          // 发射场代码
  englishName: string;   // 英文名称
  chineseName: string;   // 中文名称
  country?: string;      // 所属国家（可选）
  category: 'USA' | 'China' | 'Russia' | 'Other' | 'Commercial' | 'TestSite';  // 分类
  aliases?: string[];    // 别名列表
}

/**
 * 发射场数据
 */
export const launchSites: LaunchSite[] = [
  // 美国发射场
  {
    code: 'CC',
    englishName: 'Cape Canaveral Space Force Station',
    chineseName: '卡纳维拉尔角航天发射场',
    category: 'USA',
    aliases: [
      'Cape Canaveral SFS, FL, USA',
      'CCAFS',
      'Cape Canaveral Air Force Station',
      'Cape Kennedy Air Force Station',
      'Cape Kennedy',
      'Cape Canaveral',
      '卡纳维拉尔角'
    ]
  },
  {
    code: 'KSC',
    englishName: 'Kennedy Space Center',
    chineseName: '肯尼迪航天中心',
    category: 'USA',
    aliases: [
      'Kennedy Space Center, FL, USA',
      'NASA Kennedy',
      'John <PERSON>. Kennedy Space Center'
    ]
  },
  {
    code: 'VSFB',
    englishName: 'Vandenberg Space Force Base',
    chineseName: '范登堡空军基地',
    category: 'USA',
    aliases: [
      'Vandenberg SFB, CA, USA',
      'VAFB',
      'Vandenberg Air Force Base',
      'Western Launch Site'
    ]
  },
  {
    code: 'SPAM',
    englishName: 'Spaceport America',
    chineseName: '美国航天港',
    category: 'USA',
    aliases: [
      'Spaceport America, NM, USA',
      'Sierra County Spaceport'
    ]
  },
  {
    code: 'WFF',
    englishName: 'Wallops Flight Facility',
    chineseName: '沃洛普斯飞行设施',
    category: 'USA',
    aliases: [
      'Wallops Flight Facility, Virginia, USA',
      'NASA Wallops',
      'Wallops Island'
    ]
  },

  // 中国发射场
  {
    code: 'JQ',
    englishName: 'Jiuquan Satellite Launch Center',
    chineseName: '酒泉卫星发射中心',
    category: 'China',
    aliases: [
      'JSLC',
      'Jiuquan Satellite Launch Center, People\'s Republic of China',
      '东风航天城',
      '酒泉发射场'
    ]
  },
  {
    code: 'TYSC',
    englishName: 'Taiyuan Satellite Launch Center',
    chineseName: '太原卫星发射中心',
    category: 'China',
    aliases: [
      'TSLC',
      'Taiyuan Satellite Launch Center, People\'s Republic of China',
      '太原发射场',
      'Wuzhai'
    ]
  },
  {
    code: 'XSC',
    englishName: 'Xichang Satellite Launch Center',
    chineseName: '西昌卫星发射中心',
    category: 'China',
    aliases: [
      'XSLC',
      'Xichang Satellite Launch Center, People\'s Republic of China',
      '西昌发射场'
    ]
  },
  {
    code: 'WEN',
    englishName: 'Wenchang Space Launch Site',
    chineseName: '文昌航天发射场',
    category: 'China',
    aliases: [
      'WSLC',
      'Wenchang Satellite Launch Center',
      'Wenchang Space Launch Site, People\'s Republic of China',
      '文昌发射场'
    ]
  },

  // 俄罗斯发射场
  {
    code: 'VOST',
    englishName: 'Vostochny Cosmodrome',
    chineseName: '东方航天发射场',
    category: 'Russia',
    aliases: [
      'Vostochny Cosmodrome, Russian Federation',
      'Восточный космодром'
    ]
  },
  {
    code: 'PLSK',
    englishName: 'Plesetsk Cosmodrome',
    chineseName: '普列谢茨克航天发射场',
    category: 'Russia',
    aliases: [
      'Plesetsk Cosmodrome, Russian Federation',
      'Космодром Плесецк',
      'GIK-1'
    ]
  },
  {
    code: 'BAI',
    englishName: 'Baikonur Cosmodrome',
    chineseName: '拜科努尔航天发射场',
    category: 'Russia',
    aliases: [
      'Baikonur Cosmodrome, Republic of Kazakhstan',
      'Космодром Байконур',
      'NIIP-5',
      'Tyuratam'
    ]
  },

  // 其他国家主要发射场
  {
    code: 'SHAR',
    englishName: 'Satish Dhawan Space Centre',
    chineseName: '萨迪什·达万航天中心',
    category: 'Other',
    country: '印度',
    aliases: [
      'SDSC',
      'Satish Dhawan Space Centre, India',
      'Sriharikota Range',
      'SHAR Centre'
    ]
  },
  {
    code: 'CSG',
    englishName: 'Guiana Space Centre',
    chineseName: '圭亚那航天中心',
    category: 'Other',
    country: '法属圭亚那',
    aliases: [
      'Guiana Space Centre, French Guiana',
      'Centre Spatial Guyanais',
      'Kourou'
    ]
  },
  {
    code: 'NARO',
    englishName: 'Naro Space Center',
    chineseName: '罗老宇航中心',
    category: 'Other',
    country: '韩国',
    aliases: [
      'Naro Space Center, South Korea',
      'Naro Space Port',
      '나로우주센터'
    ]
  },
  {
    code: 'TNSC',
    englishName: 'Tanegashima Space Center',
    chineseName: '种子岛航天中心',
    category: 'Other',
    country: '日本',
    aliases: [
      'Tanegashima Space Center, Japan',
      '種子島宇宙センター',
      'JAXA Tanegashima'
    ]
  },
  {
    code: 'MAHIA',
    englishName: 'Rocket Lab Launch Complex 1',
    chineseName: '火箭实验室1号发射场',
    category: 'Other',
    country: '新西兰',
    aliases: [
      'Rocket Lab Launch Complex 1, Mahia Peninsula, New Zealand',
      'LC-1',
      'Mahia Peninsula Launch Site'
    ]
  },

  // 商业航天发射场
  {
    code: 'SPXMCG',
    englishName: 'SpaceX Starbase',
    chineseName: 'SpaceX星舰基地',
    category: 'Commercial',
    aliases: [
      'SpaceX Starbase, TX, USA',
      'Boca Chica Launch Site',
      'Starbase, Texas'
    ]
  },
  {
    code: 'ARNHEM',
    englishName: 'Arnhem Space Centre',
    chineseName: '阿纳姆航天中心',
    category: 'Commercial',
    aliases: [
      'Arnhem Space Centre, Australia',
      'Equatorial Launch Australia'
    ]
  },
  {
    code: 'KOON',
    englishName: 'Koonibba Test Range',
    chineseName: '库尼巴试验场',
    category: 'Commercial',
    aliases: [
      'Koonibba Test Range, South Australia',
      'Southern Launch Koonibba'
    ]
  },

  // 主要试验场
  {
    code: 'WS',
    englishName: 'White Sands Missile Range',
    chineseName: '白沙导弹靶场',
    category: 'TestSite',
    aliases: [
      'White Sands Missile Range, NM, USA',
      'WSMR',
      'White Sands Space Harbor'
    ]
  },
  {
    code: 'KMR',
    englishName: 'Ronald Reagan Ballistic Missile Defense Test Site',
    chineseName: '罗纳德·里根弹道导弹防御试验场',
    category: 'TestSite',
    aliases: [
      'Reagan Test Site',
      'Kwajalein Missile Range',
      'RTS'
    ]
  },
  {
    code: 'WWAY',
    englishName: 'Whalers Way Orbital Launch Complex',
    chineseName: '捕鲸人之路轨道发射综合体',
    category: 'TestSite',
    aliases: [
      'Whalers Way Orbital Launch Complex, South Australia',
      'Southern Launch WWOLC'
    ]
  }
];

/**
 * 根据发射场代码获取发射场信息
 * @param code 发射场代码
 * @returns 发射场信息，如果未找到则返回undefined
 */
export function getLaunchSiteByCode(code: string): LaunchSite | undefined {
  return launchSites.find(site => site.code === code);
}

/**
 * 根据分类获取发射场列表
 * @param category 分类
 * @returns 该分类下的所有发射场
 */
export function getLaunchSitesByCategory(category: LaunchSite['category']): LaunchSite[] {
  return launchSites.filter(site => site.category === category);
}

/**
 * 获取所有发射场代码
 * @returns 所有发射场代码数组
 */
export function getAllLaunchSiteCodes(): string[] {
  return launchSites.map(site => site.code);
}

/**
 * 根据国家获取发射场列表
 * @param country 国家名称
 * @returns 该国家的所有发射场
 */
export function getLaunchSitesByCountry(country: string): LaunchSite[] {
  return launchSites.filter(site => site.country === country);
}

/**
 * 根据名称或别名查找发射场
 * @param name 发射场名称或别名
 * @returns 匹配的发射场信息，如果未找到则返回undefined
 */
export function findLaunchSiteByName(name: string): LaunchSite | undefined {
  const normalizedName = name.toLowerCase().trim();
  return launchSites.find(site => 
    site.englishName.toLowerCase() === normalizedName ||
    site.chineseName === name ||
    site.aliases?.some(alias => alias.toLowerCase() === normalizedName)
  );
}

/**
 * 模糊搜索发射场
 * @param keyword 搜索关键词
 * @returns 匹配的发射场列表
 */
export function searchLaunchSites(keyword: string): LaunchSite[] {
  const normalizedKeyword = keyword.toLowerCase().trim();
  return launchSites.filter(site => 
    site.englishName.toLowerCase().includes(normalizedKeyword) ||
    site.chineseName.includes(keyword) ||
    site.aliases?.some(alias => alias.toLowerCase().includes(normalizedKeyword))
  );
} 